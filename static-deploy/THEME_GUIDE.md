# 主题系统使用指南

## 概述

网站现在支持5种不同的主题，每种主题都会统一应用到工具栏、导航栏、编辑区等所有界面元素。

## 可用主题

### 1. 暗夜模式 (Dark Theme)
- **背景色**: 纯黑色 (#000000)
- **文字色**: 浅灰色 (#e0e0e0)
- **适用场景**: 夜间使用，护眼，专业编程环境

### 2. 深灰模式 (Gray Theme)
- **背景色**: 深灰色 (#2d3748)
- **文字色**: 浅灰色 (#e2e8f0)
- **适用场景**: 低光环境，现代简约风格

### 3. 纸黄模式 (Paper Theme)
- **背景色**: 纸黄色 (#fef7e0)
- **文字色**: 深棕色 (#744210)
- **适用场景**: 日间使用，温暖舒适，类似纸质书籍

### 4. 森林模式 (Forest Theme)
- **背景色**: 深绿色 (#1a2e1a)
- **文字色**: 浅绿色 (#c6f6d5)
- **适用场景**: 自然风格，护眼绿色

### 5. 海洋模式 (Ocean Theme)
- **背景色**: 深蓝色 (#1a365d)
- **文字色**: 浅蓝色 (#bee3f8)
- **适用场景**: 冷色调，专业商务风格

## 如何切换主题

1. 在页面右上角找到画笔图标的主题切换按钮
2. 点击按钮打开主题选择菜单
3. 选择你喜欢的主题
4. 主题会立即应用并自动保存

## 主题特性

### 统一性
- 所有界面元素（工具栏、导航栏、编辑区）都会统一应用主题色彩
- 按钮、输入框、弹窗等组件都会自动适配主题

### 持久化
- 选择的主题会自动保存到浏览器本地存储
- 下次访问时会自动应用上次选择的主题

### 响应式
- 主题在桌面端和移动端都能正常工作
- 主题切换按钮在小屏幕设备上会自动调整大小

### 编辑器适配
- Vditor编辑器会根据主题自动调整
- 纸黄模式使用经典主题，其他模式使用暗色主题

## 技术实现

### CSS类命名
- 主题类名格式：`.theme-{主题名}`
- 例如：`.theme-dark`、`.theme-paper`

### 主题数据结构
```javascript
themes: [
    { id: 'dark', name: '暗夜模式', color: '#000000' },
    { id: 'gray', name: '深灰模式', color: '#2d3748' },
    { id: 'paper', name: '纸黄模式', color: '#fef7e0' },
    { id: 'forest', name: '森林模式', color: '#1a2e1a' },
    { id: 'ocean', name: '海洋模式', color: '#1a365d' }
]
```

### 本地存储
- 存储键：`selected-theme`
- 默认主题：`dark`

## 自定义主题

如果需要添加新主题，请按以下步骤：

1. 在CSS中添加新的主题样式类
2. 在Vue.js的themes数组中添加主题信息
3. 在applyTheme方法中添加对应的meta标签颜色

## 兼容性

- 支持所有现代浏览器
- 移动端完全兼容
- PWA模式下正常工作
