<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题展示 - Markdown在线编辑器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .showcase-title {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }

        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .theme-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .theme-card:hover {
            transform: translateY(-5px);
        }

        .theme-preview {
            height: 200px;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            height: 50px;
            display: flex;
            align-items: center;
            padding: 0 15px;
            font-weight: 600;
        }

        .preview-content {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .preview-text {
            font-size: 14px;
            line-height: 1.5;
        }

        .preview-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            align-self: flex-start;
        }

        .theme-info {
            padding: 15px;
            background: white;
            border-top: 1px solid #eee;
        }

        .theme-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .theme-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .color-info {
            font-size: 12px;
            color: #888;
        }

        /* 暗夜主题预览 */
        .theme-dark-preview {
            background: #000000;
            color: #e0e0e0;
        }
        .theme-dark-preview .preview-header {
            background: #000000;
            border-bottom: 1px solid #888;
        }
        .theme-dark-preview .preview-button {
            background: #002FA7;
            color: white;
        }

        /* 深灰主题预览 */
        .theme-gray-preview {
            background: #2d3748;
            color: #e2e8f0;
        }
        .theme-gray-preview .preview-header {
            background: #1a202c;
            border-bottom: 1px solid #4a5568;
        }
        .theme-gray-preview .preview-button {
            background: #002FA7;
            color: white;
        }

        /* 纸黄主题预览 */
        .theme-paper-preview {
            background: #fef7e0;
            color: #744210;
        }
        .theme-paper-preview .preview-header {
            background: #f6e05e;
            border-bottom: 1px solid #d69e2e;
            color: #744210;
        }
        .theme-paper-preview .preview-button {
            background: #d69e2e;
            color: white;
        }

        /* 森林主题预览 */
        .theme-forest-preview {
            background: #1a2e1a;
            color: #c6f6d5;
        }
        .theme-forest-preview .preview-header {
            background: #22543d;
            border-bottom: 1px solid #38a169;
        }
        .theme-forest-preview .preview-button {
            background: #002FA7;
            color: white;
        }

        /* 海洋主题预览 */
        .theme-ocean-preview {
            background: #1a365d;
            color: #bee3f8;
        }
        .theme-ocean-preview .preview-header {
            background: #2c5282;
            border-bottom: 1px solid #3182ce;
        }
        .theme-ocean-preview .preview-button {
            background: #002FA7;
            color: white;
        }

        .back-link {
            text-align: center;
            margin-top: 40px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <h1 class="showcase-title">Markdown在线编辑器 - 主题展示</h1>
        
        <div class="theme-grid">
            <!-- 暗夜主题 -->
            <div class="theme-card">
                <div class="theme-preview theme-dark-preview">
                    <div class="preview-header">
                        工具栏 | 导航栏
                    </div>
                    <div class="preview-content">
                        <div class="preview-text">
                            # 标题示例<br>
                            这是编辑区域的内容示例...
                        </div>
                        <button class="preview-button">主要按钮</button>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">暗夜模式</div>
                    <div class="theme-description">经典的黑色主题，适合夜间使用和专业编程环境</div>
                    <div class="color-info">背景: #000000 | 文字: #e0e0e0</div>
                </div>
            </div>

            <!-- 深灰主题 -->
            <div class="theme-card">
                <div class="theme-preview theme-gray-preview">
                    <div class="preview-header">
                        工具栏 | 导航栏
                    </div>
                    <div class="preview-content">
                        <div class="preview-text">
                            # 标题示例<br>
                            这是编辑区域的内容示例...
                        </div>
                        <button class="preview-button">主要按钮</button>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">深灰模式</div>
                    <div class="theme-description">现代简约的深灰色主题，适合低光环境使用</div>
                    <div class="color-info">背景: #2d3748 | 文字: #e2e8f0</div>
                </div>
            </div>

            <!-- 纸黄主题 -->
            <div class="theme-card">
                <div class="theme-preview theme-paper-preview">
                    <div class="preview-header">
                        工具栏 | 导航栏
                    </div>
                    <div class="preview-content">
                        <div class="preview-text">
                            # 标题示例<br>
                            这是编辑区域的内容示例...
                        </div>
                        <button class="preview-button">主要按钮</button>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">纸黄模式</div>
                    <div class="theme-description">温暖舒适的纸质书籍风格，适合日间长时间使用</div>
                    <div class="color-info">背景: #fef7e0 | 文字: #744210</div>
                </div>
            </div>

            <!-- 森林主题 -->
            <div class="theme-card">
                <div class="theme-preview theme-forest-preview">
                    <div class="preview-header">
                        工具栏 | 导航栏
                    </div>
                    <div class="preview-content">
                        <div class="preview-text">
                            # 标题示例<br>
                            这是编辑区域的内容示例...
                        </div>
                        <button class="preview-button">主要按钮</button>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">森林模式</div>
                    <div class="theme-description">自然风格的绿色主题，护眼且富有生机</div>
                    <div class="color-info">背景: #1a2e1a | 文字: #c6f6d5</div>
                </div>
            </div>

            <!-- 海洋主题 -->
            <div class="theme-card">
                <div class="theme-preview theme-ocean-preview">
                    <div class="preview-header">
                        工具栏 | 导航栏
                    </div>
                    <div class="preview-content">
                        <div class="preview-text">
                            # 标题示例<br>
                            这是编辑区域的内容示例...
                        </div>
                        <button class="preview-button">主要按钮</button>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">海洋模式</div>
                    <div class="theme-description">专业商务风格的蓝色主题，冷色调且现代</div>
                    <div class="color-info">背景: #1a365d | 文字: #bee3f8</div>
                </div>
            </div>
        </div>

        <div class="back-link">
            <a href="index.html">← 返回编辑器</a>
        </div>
    </div>
</body>
</html>
