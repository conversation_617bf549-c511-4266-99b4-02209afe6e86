<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端图床调试</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            padding: 10px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            margin-bottom: 15px;
            color: #333;
        }
        
        h1 {
            font-size: 24px;
            text-align: center;
        }
        
        h2 {
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            -webkit-appearance: none;
        }
        
        button {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            font-size: 16px;
            cursor: pointer;
            -webkit-appearance: none;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .recommendations {
            margin-top: 20px;
            padding: 15px;
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            border-radius: 4px;
        }

        .recommendations h4 {
            margin: 0 0 10px 0;
            color: #e65100;
        }

        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }

        .recommendations li {
            margin-bottom: 5px;
            color: #bf360c;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .loading {
            text-align: center;
            color: #666;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>📱 移动端图床调试工具</h1>
    
    <div class="container">
        <h2>🔍 设备信息</h2>
        <div id="device-info"></div>
    </div>

    <div class="container">
        <h2>🌐 网络状态</h2>
        <button onclick="checkNetwork()">检查网络状态</button>
        <div id="network-status"></div>
    </div>

    <div class="container">
        <h2>⚙️ GitHub配置</h2>
        <div class="form-group">
            <label for="token">GitHub Token:</label>
            <input type="password" id="token" placeholder="ghp_xxxxxxxxxxxx">
        </div>
        <div class="form-group">
            <label for="owner">仓库所有者:</label>
            <input type="text" id="owner" placeholder="SUNSIR007">
        </div>
        <div class="form-group">
            <label for="repo">仓库名称:</label>
            <input type="text" id="repo" placeholder="picx-images-hosting">
        </div>
        <div class="form-group">
            <label for="branch">分支名称:</label>
            <input type="text" id="branch" value="master" placeholder="master">
        </div>
        <button onclick="testGitHubConfig()" id="test-btn">测试GitHub连接</button>
        <div id="github-result"></div>
    </div>

    <div class="container">
        <h2>🔧 详细诊断</h2>
        <button onclick="runFullDiagnosis()">运行完整诊断</button>
        <div id="diagnosis-result"></div>
    </div>

    <script src="image-service.js"></script>
    <script>
        // 显示设备信息
        function showDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screen: {
                    width: screen.width,
                    height: screen.height,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight
                },
                window: {
                    innerWidth: window.innerWidth,
                    innerHeight: window.innerHeight
                },
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt,
                    saveData: navigator.connection.saveData
                } : 'Not available',
                isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
                isIOS: /iPhone|iPad|iPod/i.test(navigator.userAgent),
                isAndroid: /Android/i.test(navigator.userAgent),
                isSafari: /Safari/i.test(navigator.userAgent) && !/Chrome/i.test(navigator.userAgent),
                isChrome: /Chrome/i.test(navigator.userAgent),
                isFirefox: /Firefox/i.test(navigator.userAgent)
            };
            
            document.getElementById('device-info').innerHTML = `<pre>${JSON.stringify(info, null, 2)}</pre>`;
        }

        // 检查网络状态
        async function checkNetwork() {
            const resultDiv = document.getElementById('network-status');
            resultDiv.innerHTML = '<div class="loading">正在检查网络环境...</div>';

            try {
                // 使用新的网络环境检测方法
                const imageService = new ImageService();
                const networkInfo = await imageService.detectNetworkEnvironment();

                // 显示环境信息
                let html = '<div class="result-grid">';

                // 环境类型
                html += `
                    <div class="result info">
                        <strong>网络环境:</strong> ${networkInfo.environment}
                        ${networkInfo.proxy_detected ? ' (检测到代理)' : ''}
                    </div>
                `;

                // GitHub API状态
                const apiStatus = networkInfo.github_api.accessible ? '✅ 连接成功' : '❌ 连接失败';
                const apiClass = networkInfo.github_api.accessible ? 'success' : 'error';
                html += `
                    <div class="result ${apiClass}">
                        <strong>GitHub API:</strong> ${apiStatus}
                        ${networkInfo.github_api.time ? `(${networkInfo.github_api.time}ms, 200)` : ''}
                        ${networkInfo.github_api.error ? `<br><small>错误: ${networkInfo.github_api.error}</small>` : ''}
                    </div>
                `;

                // GitHub主站状态
                const pagesStatus = networkInfo.github_pages.accessible ? '✅ 连接成功' : '❌ 连接失败';
                const pagesClass = networkInfo.github_pages.accessible ? 'success' : 'error';
                html += `
                    <div class="result ${pagesClass}">
                        <strong>GitHub主站:</strong> ${pagesStatus}
                        ${networkInfo.github_pages.time ? `(${networkInfo.github_pages.time}ms)` : ''}
                        ${networkInfo.github_pages.error ? `<br><small>错误: ${networkInfo.github_pages.error}</small>` : ''}
                    </div>
                `;

                // jsDelivr CDN状态
                const cdnStatus = networkInfo.jsdelivr_cdn.accessible ? '✅ 连接成功' : '❌ 连接失败';
                const cdnClass = networkInfo.jsdelivr_cdn.accessible ? 'success' : 'error';
                html += `
                    <div class="result ${cdnClass}">
                        <strong>jsDelivr CDN:</strong> ${cdnStatus}
                        ${networkInfo.jsdelivr_cdn.time ? `(${networkInfo.jsdelivr_cdn.time}ms, 200)` : ''}
                        ${networkInfo.jsdelivr_cdn.error ? `<br><small>错误: ${networkInfo.jsdelivr_cdn.error}</small>` : ''}
                    </div>
                `;

                html += '</div>';

                // 显示建议
                if (networkInfo.recommendations.length > 0) {
                    html += '<div class="recommendations"><h4>💡 建议:</h4><ul>';
                    networkInfo.recommendations.forEach(rec => {
                        html += `<li>${rec}</li>`;
                    });
                    html += '</ul></div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>网络检测失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // 测试GitHub配置
        async function testGitHubConfig() {
            const token = document.getElementById('token').value.trim();
            const owner = document.getElementById('owner').value.trim();
            const repo = document.getElementById('repo').value.trim();
            const branch = document.getElementById('branch').value.trim() || 'master';

            const resultDiv = document.getElementById('github-result');
            const testBtn = document.getElementById('test-btn');
            
            if (!token || !owner || !repo) {
                resultDiv.innerHTML = '<div class="result error">❌ 请填写完整的配置信息</div>';
                return;
            }

            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            resultDiv.innerHTML = '<div class="loading">正在测试GitHub连接...</div>';

            try {
                const imageService = new ImageService();
                imageService.setConfig({ token, owner, repo, branch });
                
                console.log('开始测试GitHub连接...');
                const result = await imageService.testConnection();
                console.log('测试结果:', result);
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        ✅ 连接成功！<br>
                        👤 用户: ${result.user}<br>
                        📁 仓库: ${result.repo}<br>
                        🔑 权限: ${result.permissions}
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 连接失败: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('测试GitHub配置失败:', error);
                resultDiv.innerHTML = `<div class="result error">❌ 测试失败: ${error.message}</div>`;
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '测试GitHub连接';
            }
        }

        // 运行完整诊断
        async function runFullDiagnosis() {
            const resultDiv = document.getElementById('diagnosis-result');
            resultDiv.innerHTML = '<div class="loading">正在运行完整诊断...</div>';

            const steps = [];

            // 步骤1: 基础API测试
            steps.push({
                title: '1. 基础API连通性测试',
                test: async () => {
                    try {
                        const response = await fetch('https://api.github.com/zen');
                        const text = await response.text();
                        return { success: true, message: `GitHub API可访问，响应: ${text}` };
                    } catch (error) {
                        return { success: false, message: `GitHub API不可访问: ${error.message}` };
                    }
                }
            });

            // 步骤2: CORS测试
            steps.push({
                title: '2. CORS支持测试',
                test: async () => {
                    try {
                        const response = await fetch('https://api.github.com/zen', {
                            method: 'GET',
                            mode: 'cors',
                            credentials: 'omit'
                        });
                        return { success: true, message: 'CORS请求成功' };
                    } catch (error) {
                        return { success: false, message: `CORS请求失败: ${error.message}` };
                    }
                }
            });

            // 步骤3: 认证测试（如果有配置）
            const token = document.getElementById('token').value.trim();
            if (token) {
                steps.push({
                    title: '3. GitHub认证测试',
                    test: async () => {
                        try {
                            const response = await fetch('https://api.github.com/user', {
                                headers: {
                                    'Authorization': `token ${token}`,
                                    'Accept': 'application/vnd.github.v3+json'
                                }
                            });
                            if (response.ok) {
                                const user = await response.json();
                                return { success: true, message: `认证成功，用户: ${user.login}` };
                            } else {
                                return { success: false, message: `认证失败: ${response.status} ${response.statusText}` };
                            }
                        } catch (error) {
                            return { success: false, message: `认证请求失败: ${error.message}` };
                        }
                    }
                });
            }

            let html = '';
            for (const step of steps) {
                html += `<div class="step">
                    <div class="step-title">${step.title}</div>
                    <div class="loading">测试中...</div>
                </div>`;
            }
            resultDiv.innerHTML = html;

            // 逐步执行测试
            const stepDivs = resultDiv.querySelectorAll('.step');
            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                const stepDiv = stepDivs[i];
                
                try {
                    const result = await step.test();
                    const resultClass = result.success ? 'success' : 'error';
                    const resultIcon = result.success ? '✅' : '❌';
                    stepDiv.innerHTML = `
                        <div class="step-title">${step.title}</div>
                        <div class="result ${resultClass}">${resultIcon} ${result.message}</div>
                    `;
                } catch (error) {
                    stepDiv.innerHTML = `
                        <div class="step-title">${step.title}</div>
                        <div class="result error">❌ 测试异常: ${error.message}</div>
                    `;
                }
            }
        }

        // 页面加载时显示设备信息
        showDeviceInfo();
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            document.getElementById('network-status').innerHTML = 
                '<div class="result success">✅ 网络已连接</div>';
        });
        
        window.addEventListener('offline', () => {
            document.getElementById('network-status').innerHTML = 
                '<div class="result error">❌ 网络已断开</div>';
        });
    </script>
</body>
</html>
