<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s ease;
        }

        /* 暗夜主题 */
        .theme-dark {
            background: #000000;
            color: #e0e0e0;
        }

        /* 深灰主题 */
        .theme-gray {
            background: #2d3748;
            color: #e2e8f0;
        }

        /* 纸黄主题 */
        .theme-paper {
            background: #fef7e0;
            color: #744210;
        }

        /* 森林主题 */
        .theme-forest {
            background: #1a2e1a;
            color: #c6f6d5;
        }

        /* 海洋主题 */
        .theme-ocean {
            background: #1a365d;
            color: #bee3f8;
        }

        .theme-selector {
            margin-bottom: 20px;
        }

        .theme-button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .theme-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .content-area {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid;
        }

        .theme-dark .content-area {
            background: #1a1a1a;
            border-color: #333;
        }

        .theme-gray .content-area {
            background: #1a202c;
            border-color: #4a5568;
        }

        .theme-paper .content-area {
            background: #fffbeb;
            border-color: #d69e2e;
        }

        .theme-forest .content-area {
            background: #22543d;
            border-color: #38a169;
        }

        .theme-ocean .content-area {
            background: #2c5282;
            border-color: #3182ce;
        }
    </style>
</head>
<body class="theme-dark">
    <h1>主题测试页面</h1>
    
    <div class="theme-selector">
        <h2>选择主题：</h2>
        <button class="theme-button" onclick="setTheme('dark')" style="background: #000; color: white;">暗夜模式</button>
        <button class="theme-button" onclick="setTheme('gray')" style="background: #2d3748; color: white;">深灰模式</button>
        <button class="theme-button" onclick="setTheme('paper')" style="background: #fef7e0; color: #744210;">纸黄模式</button>
        <button class="theme-button" onclick="setTheme('forest')" style="background: #1a2e1a; color: #c6f6d5;">森林模式</button>
        <button class="theme-button" onclick="setTheme('ocean')" style="background: #1a365d; color: #bee3f8;">海洋模式</button>
    </div>

    <div class="content-area">
        <h3>内容区域</h3>
        <p>这是一个测试内容区域，用来展示不同主题下的效果。</p>
        <ul>
            <li>工具栏颜色</li>
            <li>导航栏颜色</li>
            <li>编辑区颜色</li>
            <li>文字颜色</li>
        </ul>
    </div>

    <div class="content-area">
        <h3>编辑器模拟</h3>
        <textarea style="width: 100%; height: 200px; background: inherit; color: inherit; border: 1px solid; border-radius: 4px; padding: 10px;" placeholder="这里模拟编辑器内容..."></textarea>
    </div>

    <script>
        function setTheme(themeName) {
            // 移除所有主题类
            document.body.classList.remove('theme-dark', 'theme-gray', 'theme-paper', 'theme-forest', 'theme-ocean');
            // 添加新主题类
            document.body.classList.add(`theme-${themeName}`);
            
            console.log(`切换到主题: ${themeName}`);
        }
    </script>
</body>
</html>
