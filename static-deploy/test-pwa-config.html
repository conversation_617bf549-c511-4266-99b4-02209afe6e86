<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA配置检测测试</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.13/lib/index.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #409EFF;
            background: #f0f9ff;
        }
        .status-true {
            border-left-color: #67C23A;
            background: #f0f9ff;
        }
        .status-false {
            border-left-color: #F56C6C;
            background: #fef0f0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h2>PWA配置检测测试</h2>
            
            <div class="status-item" :class="isPWA ? 'status-true' : 'status-false'">
                <strong>PWA模式:</strong> {{ isPWA ? '是' : '否' }}
            </div>
            
            <div class="status-item" :class="hasStandalone ? 'status-true' : 'status-false'">
                <strong>navigator.standalone:</strong> {{ hasStandalone }}
            </div>
            
            <div class="status-item" :class="hasDisplayMode ? 'status-true' : 'status-false'">
                <strong>display-mode: standalone:</strong> {{ hasDisplayMode }}
            </div>
            
            <div class="status-item" :class="hasImageConfig ? 'status-true' : 'status-false'">
                <strong>图片服务配置:</strong> {{ hasImageConfig ? '已配置' : '未配置' }}
            </div>
            
            <div class="status-item" :class="hasGitHubConfig ? 'status-true' : 'status-false'">
                <strong>GitHub配置:</strong> {{ hasGitHubConfig ? '已配置' : '未配置' }}
            </div>
            
            <div style="margin-top: 20px;">
                <el-button @click="clearAllConfig" type="danger">清除所有配置</el-button>
                <el-button @click="setTestConfig" type="primary">设置测试配置</el-button>
                <el-button @click="refreshStatus" type="info">刷新状态</el-button>
            </div>
            
            <div style="margin-top: 20px;">
                <el-button @click="testPWADialog" type="warning">测试PWA配置对话框</el-button>
            </div>
            
            <div style="margin-top: 20px; font-size: 12px; color: #666;">
                <p><strong>使用说明:</strong></p>
                <ul>
                    <li>在Safari中打开此页面，然后点击"添加到主屏幕"</li>
                    <li>从主屏幕打开应用，观察PWA模式检测结果</li>
                    <li>比较浏览器模式和PWA模式下的配置状态差异</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    isPWA: false,
                    hasStandalone: false,
                    hasDisplayMode: false,
                    hasImageConfig: false,
                    hasGitHubConfig: false
                };
            },
            mounted() {
                this.refreshStatus();
            },
            methods: {
                refreshStatus() {
                    // 检测PWA模式
                    this.hasStandalone = window.navigator.standalone === true;
                    this.hasDisplayMode = window.matchMedia('(display-mode: standalone)').matches;
                    this.isPWA = this.hasStandalone || this.hasDisplayMode;
                    
                    // 检查配置
                    this.hasImageConfig = !!localStorage.getItem('image-service-config');
                    this.hasGitHubConfig = !!localStorage.getItem('github-config');
                },
                
                clearAllConfig() {
                    localStorage.removeItem('image-service-config');
                    localStorage.removeItem('github-config');
                    localStorage.removeItem('image-service-link-rule');
                    this.refreshStatus();
                    this.$message.success('所有配置已清除');
                },
                
                setTestConfig() {
                    const imageConfig = {
                        token: 'test-token',
                        owner: 'SUNSIR007',
                        repo: 'picx-images-hosting',
                        branch: 'master',
                        imageDir: 'images'
                    };
                    
                    const githubConfig = {
                        token: 'test-token',
                        owner: 'SUNSIR007',
                        repo: 'test-repo'
                    };
                    
                    localStorage.setItem('image-service-config', JSON.stringify(imageConfig));
                    localStorage.setItem('github-config', JSON.stringify(githubConfig));
                    
                    this.refreshStatus();
                    this.$message.success('测试配置已设置');
                },
                
                testPWADialog() {
                    this.$confirm(
                        'PWA模式下需要重新配置。这是因为PWA和浏览器使用不同的存储空间。是否现在配置？',
                        'PWA配置提醒',
                        {
                            confirmButtonText: '立即配置',
                            cancelButtonText: '稍后配置',
                            type: 'info',
                            customClass: 'pwa-config-dialog'
                        }
                    ).then(() => {
                        this.$message.success('用户选择立即配置');
                    }).catch(() => {
                        this.$message.warning('用户选择稍后配置');
                    });
                }
            }
        });
    </script>
</body>
</html>
