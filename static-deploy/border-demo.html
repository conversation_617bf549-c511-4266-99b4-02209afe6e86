<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑区分割线效果演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s ease;
        }

        /* 主题样式 */
        .theme-dark {
            background: #000000;
            color: #e0e0e0;
        }

        .theme-gray {
            background: #2d3748;
            color: #e2e8f0;
        }

        .theme-paper {
            background: #fef7e0;
            color: #744210;
        }

        .theme-forest {
            background: #1a2e1a;
            color: #c6f6d5;
        }

        .theme-ocean {
            background: #1a365d;
            color: #bee3f8;
        }

        /* 头部样式 */
        .header {
            padding: 15px 25px;
            color: white;
            border-bottom: none;
        }

        .theme-dark .header {
            background: #000000;
            border-bottom-color: #888;
        }

        .theme-gray .header {
            background: #1a202c;
            border-bottom-color: #4a5568;
        }

        .theme-paper .header {
            background: #f6e05e;
            border-bottom-color: #d69e2e;
            color: #744210;
        }

        .theme-forest .header {
            background: #22543d;
            border-bottom-color: #38a169;
        }

        .theme-ocean .header {
            background: #2c5282;
            border-bottom-color: #3182ce;
        }

        /* 编辑器容器样式 */
        .editor-container {
            height: calc(100vh - 130px);
            margin: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 2px solid;
            position: relative;
        }

        .theme-dark .editor-container {
            background: #000000;
            border-color: #333333;
            box-shadow: 0 4px 20px rgba(255,255,255,0.05);
        }

        .theme-gray .editor-container {
            background: #2d3748;
            border-color: #4a5568;
            box-shadow: 0 4px 20px rgba(255,255,255,0.05);
        }

        .theme-paper .editor-container {
            background: #fef7e0;
            border-color: #d69e2e;
            box-shadow: 0 4px 20px rgba(214,158,46,0.2);
        }

        .theme-forest .editor-container {
            background: #1a2e1a;
            border-color: #38a169;
            box-shadow: 0 4px 20px rgba(56,161,105,0.2);
        }

        .theme-ocean .editor-container {
            background: #1a365d;
            border-color: #3182ce;
            box-shadow: 0 4px 20px rgba(49,130,206,0.2);
        }

        .editor-content {
            padding: 20px;
            height: 100%;
            overflow-y: auto;
        }

        .theme-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .theme-button {
            padding: 8px 16px;
            margin: 2px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .theme-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .demo-text {
            line-height: 1.6;
        }

        .highlight-box {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .theme-dark .highlight-box {
            background: #1a1a1a;
            border-left-color: #333;
        }

        .theme-gray .highlight-box {
            background: #1a202c;
            border-left-color: #4a5568;
        }

        .theme-paper .highlight-box {
            background: #fffbeb;
            border-left-color: #d69e2e;
        }

        .theme-forest .highlight-box {
            background: #22543d;
            border-left-color: #38a169;
        }

        .theme-ocean .highlight-box {
            background: #2c5282;
            border-left-color: #3182ce;
        }

        @media (max-width: 768px) {
            .editor-container {
                margin: 15px;
                border-radius: 8px;
            }
        }

        @media (max-width: 480px) {
            .editor-container {
                margin: 8px;
                border-radius: 6px;
                border-width: 1px;
            }
        }
    </style>
</head>
<body class="theme-paper">
    <div class="theme-selector">
        <button class="theme-button" onclick="setTheme('dark')" style="background: #000; color: white;">暗夜</button>
        <button class="theme-button" onclick="setTheme('gray')" style="background: #2d3748; color: white;">深灰</button>
        <button class="theme-button" onclick="setTheme('paper')" style="background: #fef7e0; color: #744210;">纸黄</button>
        <button class="theme-button" onclick="setTheme('forest')" style="background: #1a2e1a; color: #c6f6d5;">森林</button>
        <button class="theme-button" onclick="setTheme('ocean')" style="background: #1a365d; color: #bee3f8;">海洋</button>
    </div>

    <div class="header">
        <h2>编辑区分割线效果演示</h2>
        <p>工具栏和导航栏区域</p>
    </div>

    <div class="editor-container">
        <div class="editor-content">
            <h3>编辑区域</h3>
            <p class="demo-text">
                这是编辑区域的内容。现在编辑区有了明显的分割线边框，可以清楚地区分编辑区域和其他界面元素。
            </p>
            
            <div class="highlight-box">
                <h4>特性说明</h4>
                <ul>
                    <li>✅ 圆角边框设计，现代美观</li>
                    <li>✅ 不同主题使用不同的边框颜色</li>
                    <li>✅ 阴影效果增强立体感</li>
                    <li>✅ 响应式设计，适配各种屏幕</li>
                    <li>✅ 内容区域有适当的内边距</li>
                </ul>
            </div>

            <p class="demo-text">
                边框颜色会根据当前主题自动调整：
            </p>
            <ul>
                <li><strong>暗夜模式</strong>：深灰色边框 (#333333)</li>
                <li><strong>深灰模式</strong>：中灰色边框 (#4a5568)</li>
                <li><strong>纸黄模式</strong>：金黄色边框 (#d69e2e)</li>
                <li><strong>森林模式</strong>：绿色边框 (#38a169)</li>
                <li><strong>海洋模式</strong>：蓝色边框 (#3182ce)</li>
            </ul>

            <div class="highlight-box">
                <h4>视觉效果</h4>
                <p>分割线不仅起到了区分区域的作用，还增强了整体的视觉层次感，让编辑区域更加突出和专业。</p>
            </div>
        </div>
    </div>

    <script>
        function setTheme(themeName) {
            document.body.classList.remove('theme-dark', 'theme-gray', 'theme-paper', 'theme-forest', 'theme-ocean');
            document.body.classList.add(`theme-${themeName}`);
            console.log(`切换到主题: ${themeName}`);
        }
    </script>
</body>
</html>
