<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图床调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        textarea {
            height: 100px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>图床调试测试</h1>
    
    <div class="section">
        <h2>环境信息</h2>
        <div id="env-info"></div>
    </div>

    <div class="section">
        <h2>配置测试</h2>
        <div>
            <label>GitHub Token:</label>
            <input type="password" id="token" placeholder="输入GitHub Token">
        </div>
        <div>
            <label>仓库所有者:</label>
            <input type="text" id="owner" placeholder="例如: SUNSIR007">
        </div>
        <div>
            <label>仓库名称:</label>
            <input type="text" id="repo" placeholder="例如: picx-images-hosting">
        </div>
        <div>
            <label>分支名称:</label>
            <input type="text" id="branch" value="master" placeholder="例如: master">
        </div>
        <button onclick="testConfig()">测试配置</button>
        <div id="config-result"></div>
    </div>

    <div class="section">
        <h2>网络测试</h2>
        <button onclick="testNetwork()">测试网络连接</button>
        <div id="network-result"></div>
    </div>

    <div class="section">
        <h2>API测试</h2>
        <button onclick="testAPI()">测试GitHub API</button>
        <div id="api-result"></div>
    </div>

    <script src="image-service.js"></script>
    <script>
        // 显示环境信息
        function showEnvInfo() {
            const info = {
                userAgent: navigator.userAgent,
                isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host
            };
            
            document.getElementById('env-info').innerHTML = `<pre>${JSON.stringify(info, null, 2)}</pre>`;
        }

        // 测试配置
        async function testConfig() {
            const token = document.getElementById('token').value;
            const owner = document.getElementById('owner').value;
            const repo = document.getElementById('repo').value;
            const branch = document.getElementById('branch').value;

            const resultDiv = document.getElementById('config-result');
            
            if (!token || !owner || !repo) {
                resultDiv.innerHTML = '<div class="result error">请填写完整的配置信息</div>';
                return;
            }

            try {
                const imageService = new ImageService();
                imageService.setConfig({ token, owner, repo, branch });
                
                resultDiv.innerHTML = '<div class="result info">正在测试连接...</div>';
                
                const result = await imageService.testConnection();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        连接成功！<br>
                        用户: ${result.user}<br>
                        仓库: ${result.repo}<br>
                        权限: ${result.permissions}
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">连接失败: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('测试配置失败:', error);
                resultDiv.innerHTML = `<div class="result error">测试失败: ${error.message}</div>`;
            }
        }

        // 测试网络连接
        async function testNetwork() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.innerHTML = '<div class="result info">正在测试网络连接...</div>';

            const tests = [
                { name: 'GitHub API', url: 'https://api.github.com' },
                { name: 'GitHub主站', url: 'https://github.com' },
                { name: 'Google DNS', url: 'https://8.8.8.8' }
            ];

            let results = [];

            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url, { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache'
                    });
                    const endTime = Date.now();
                    
                    results.push({
                        name: test.name,
                        status: 'success',
                        time: endTime - startTime,
                        message: `连接成功 (${endTime - startTime}ms)`
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        message: error.message
                    });
                }
            }

            const html = results.map(result => 
                `<div class="result ${result.status}">
                    ${result.name}: ${result.message}
                </div>`
            ).join('');

            resultDiv.innerHTML = html;
        }

        // 测试API
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="result info">正在测试API...</div>';

            try {
                // 测试不需要认证的API
                const response = await fetch('https://api.github.com/zen');
                const text = await response.text();
                
                resultDiv.innerHTML = `<div class="result success">
                    GitHub API 可访问<br>
                    响应: ${text}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
                    GitHub API 不可访问: ${error.message}
                </div>`;
            }
        }

        // 页面加载时显示环境信息
        showEnvInfo();
    </script>
</body>
</html>
