<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图床配置调试工具</h1>
        
        <div class="config-item">
            <h3>当前localStorage配置</h3>
            <pre id="localStorage-config">加载中...</pre>
        </div>
        
        <div class="config-item">
            <h3>当前imageService配置</h3>
            <pre id="imageService-config">加载中...</pre>
        </div>
        
        <div class="config-item">
            <h3>操作</h3>
            <button onclick="clearConfig()">清除所有配置</button>
            <button onclick="setCorrectConfig()">设置正确的master分支配置</button>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="refreshConfig()">刷新配置显示</button>
        </div>
        
        <div class="config-item">
            <h3>测试结果</h3>
            <div id="test-result">等待测试...</div>
        </div>
    </div>

    <script src="image-service.js"></script>
    <script>
        // 初始化imageService
        if (!window.imageService) {
            window.imageService = new ImageService();
        }

        function refreshConfig() {
            // 显示localStorage配置
            const savedConfig = localStorage.getItem('image-service-config');
            document.getElementById('localStorage-config').textContent = 
                savedConfig ? JSON.stringify(JSON.parse(savedConfig), null, 2) : '无配置';
            
            // 显示imageService配置
            const serviceConfig = {
                token: window.imageService.token ? '***已设置***' : '未设置',
                owner: window.imageService.owner,
                repo: window.imageService.repo,
                branch: window.imageService.branch,
                imageDir: window.imageService.imageDir,
                isConfigured: window.imageService.isConfigured()
            };
            document.getElementById('imageService-config').textContent = 
                JSON.stringify(serviceConfig, null, 2);
        }

        function clearConfig() {
            localStorage.removeItem('image-service-config');
            localStorage.removeItem('image-service-link-rule');
            window.imageService = new ImageService();
            refreshConfig();
            document.getElementById('test-result').innerHTML = 
                '<div class="success">配置已清除</div>';
        }

        function setCorrectConfig() {
            const config = {
                token: prompt('请输入GitHub Token:'),
                owner: 'SUNSIR007',
                repo: 'picx-images-hosting',
                branch: 'master',
                imageDir: 'images'
            };
            
            if (config.token) {
                localStorage.setItem('image-service-config', JSON.stringify(config));
                window.imageService.setConfig(config);
                refreshConfig();
                document.getElementById('test-result').innerHTML = 
                    '<div class="success">配置已设置为master分支</div>';
            }
        }

        async function testConnection() {
            try {
                document.getElementById('test-result').innerHTML = '测试中...';
                
                // 加载保存的配置
                const savedConfig = localStorage.getItem('image-service-config');
                if (savedConfig) {
                    window.imageService.setConfig(JSON.parse(savedConfig));
                }
                
                const result = await window.imageService.testConnection();
                document.getElementById('test-result').innerHTML = 
                    `<div class="success">连接成功: ${result.message}</div>`;
            } catch (error) {
                document.getElementById('test-result').innerHTML = 
                    `<div class="error">连接失败: ${error.message}</div>`;
            }
        }

        // 页面加载时刷新配置
        window.addEventListener('load', () => {
            // 加载保存的配置
            const savedConfig = localStorage.getItem('image-service-config');
            if (savedConfig) {
                window.imageService.setConfig(JSON.parse(savedConfig));
            }
            refreshConfig();
        });
    </script>
</body>
</html>
